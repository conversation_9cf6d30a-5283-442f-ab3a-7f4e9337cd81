/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RequestValidator.h

Abstract:

    Header file for the Request Validator component that implements enhanced request filtering
    for the AIMX server. This component provides three-gate validation:
    1. Enterprise/IT Management Request Validation
    2. Single vs Multi-Step Task Detection  
    3. Read-Only Operation Enforcement

Author:

    AI Assistant (Generated) 07/30/2025

--*/

#pragma once
#include "AimxCommon.h"
#include "ConversationManager.h"
#include <nlohmann/json.hpp>
#include <memory>

// Validation result types
enum AIMX_VALIDATION_RESULT
{
    AIMX_VALIDATION_PASSED = 0,
    AIMX_VALIDATION_NOT_ENTERPRISE_IT = 1,
    AIMX_VALIDATION_MULTI_STEP_REQUIRED = 2,
    AIMX_VALIDATION_MODIFICATION_BLOCKED = 3,
    AIMX_VALIDATION_ERROR = 4
};

// Validation gate types
enum AIMX_VALIDATION_GATE
{
    AIMX_GATE_ENTERPRISE_IT = 1,
    AI<PERSON>_GATE_SINGLE_MULTI_STEP = 2,
    AIMX_GATE_READ_ONLY_ENFORCEMENT = 3
};

// Validation context structure
struct AIMX_VALIDATION_CONTEXT
{
    std::wstring UserQuery;
    std::wstring ContextGuid;
    AIMX_EXECUTION_MODE ExecutionMode;
    std::shared_ptr<ConversationSession> ConversationSession;
    nlohmann::json ValidationMetadata;
};

// Validation response structure
struct AIMX_VALIDATION_RESPONSE
{
    AIMX_VALIDATION_RESULT Result;
    std::wstring Message;
    std::wstring DetailedReason;
    std::vector<std::wstring> SuggestedSteps;
    nlohmann::json ResponseData;
    bool ShouldContinueProcessing;
};

class RequestValidator
{
public:
    // Initialize the Request Validator component
    static HRESULT Initialize();

    // Uninitialize and cleanup resources
    static void Uninitialize();

    // Main validation entry point - runs all three gates
    static HRESULT ValidateRequest(
        _In_ const AIMX_VALIDATION_CONTEXT& context,
        _Out_ AIMX_VALIDATION_RESPONSE& response
        );

    // Individual gate validation methods
    static HRESULT ValidateEnterpriseITRequest(
        _In_ const AIMX_VALIDATION_CONTEXT& context,
        _Out_ AIMX_VALIDATION_RESPONSE& response
        );

    static HRESULT ValidateSingleVsMultiStep(
        _In_ const AIMX_VALIDATION_CONTEXT& context,
        _Out_ AIMX_VALIDATION_RESPONSE& response
        );

    static HRESULT ValidateReadOnlyOperation(
        _In_ const AIMX_VALIDATION_CONTEXT& context,
        _Out_ AIMX_VALIDATION_RESPONSE& response
        );

    // Helper methods for creating validation responses
    static HRESULT CreateValidationErrorResponse(
        _In_ AIMX_VALIDATION_RESULT result,
        _In_ const std::wstring& message,
        _In_ const std::wstring& reason,
        _Out_ nlohmann::json& responseJson
        );

private:
    // Private instance for LLM communication
    static RequestValidator* s_instance;
    static std::mutex s_instanceMutex;

    // Private helper methods
    HRESULT SendValidationLlmRequest(
        _In_ const std::wstring& systemPrompt,
        _In_ const std::wstring& userQuery,
        _Out_ std::wstring& response
        );

    HRESULT ParseEnterpriseValidationResponse(
        _In_ const std::wstring& llmResponse,
        _Out_ bool& isEnterpriseIT,
        _Out_ std::wstring& reason
        );

    HRESULT ParseMultiStepResponse(
        _In_ const std::wstring& llmResponse,
        _Out_ bool& isMultiStep,
        _Out_ std::vector<std::wstring>& suggestedSteps,
        _Out_ std::wstring& reason
        );

    HRESULT ParseReadOnlyResponse(
        _In_ const std::wstring& llmResponse,
        _Out_ bool& isModificationOperation,
        _Out_ std::wstring& reason
        );

    // System prompt generation methods
    std::wstring GetEnterpriseValidationPrompt();
    std::wstring GetMultiStepDetectionPrompt();
    std::wstring GetReadOnlyValidationPrompt();

    // Internal initialization
    HRESULT InitializeInternal();
};
