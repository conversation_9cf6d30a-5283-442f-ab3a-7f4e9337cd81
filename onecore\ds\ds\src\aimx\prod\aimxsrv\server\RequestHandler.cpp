/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    RequestHandler.cpp

Abstract:
    Implementation of AIMX Request Handler that processes different types of requests
    including chatbot queries, direct queries, plan status, execution, and cancellation.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 06/03/2025

--*/

#include "pch.hxx"
#include "RequestHandler.h"
#include "RequestValidator.h"
#include "AimxCommon.h"
#include "AimxConstants.h"
#include "AimxRpcServer.h"
#include "Planner.h"
#include "Orchestrator.h"
#include "ConversationManager.h"
#include "StringUtils.h"
#include "RequestHandler.cpp.tmh"


// External declarations for global operation tracking
extern std::unordered_map<GUID, std::shared_ptr<AIMX_OPERATION>, GuidHash, GuidEqual> g_OperationMap;
extern std::mutex g_OperationMapMutex;

// Global map to track OperationId -> Context GUID
std::unordered_map<GUID, GUID, GuidHash, GuidEqual> g_OperationToContextMap;
std::mutex g_OperationToContextMapMutex;
// Helper to validate OperationId belongs to context GUID
bool
RequestHandler::IsOperationOwnedByContext(
    _In_ const GUID& operationId,
    _In_ const GUID& contextGuid
    )
/*++
Routine Description:
    Checks if the given operationId is associated with the specified contextGuid in the global operation-to-context map.

Arguments:
    operationId - The operation GUID to check.
    contextGuid - The context GUID to validate ownership.

Return Value:
    true if the operation belongs to the context, false otherwise.
--*/
{
    std::lock_guard<std::mutex> lock(g_OperationToContextMapMutex);
    auto it = g_OperationToContextMap.find(operationId);
    if (it == g_OperationToContextMap.end())
        return false;
    return IsEqualGUID(it->second, contextGuid) != 0;
}


HRESULT
RequestHandler::ProcessRequest(
    _In_ AIMXR_HANDLE contextHandle,
    _In_ const nlohmann::json& requestJson,
    _Out_ nlohmann::json& responseJson
    )
/*++
Routine Description:
    Entry point for processing all AIMX requests. Dispatches to the appropriate handler based on request type.

Arguments:
    contextHandle - The context handle for the session.
    requestJson   - The JSON request to process.
    responseJson  - The JSON response to be populated.

Return Value:
    S_OK on success, or an error HRESULT on failure.
--*/
{
    TraceInfo(AimxRequest, "Entry");
    HRESULT hr = S_OK;

    // Validate common fields (requestType, etc.)
    hr = ValidateCommonFields(requestJson);
    if (FAILED(hr))
    {
        CreateErrorResponse(responseJson, hr, "Invalid or missing common request fields");
        TraceErr(AimxRequest, "ValidateCommonFields failed: %!HRESULT!", hr);
        return hr;
    }

    // Process request based on type
    AIMX_REQUEST_TYPE requestType = static_cast<AIMX_REQUEST_TYPE>(requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_REQUEST_TYPE].get<int>());

    switch (requestType)
    {
    case AIMX_CHATBOT_QUERY:
        hr = RequestHandler::ProcessChatbotQuery(contextHandle, requestJson, responseJson);
        break;

    case AIMX_DIRECT_QUERY:
        hr = RequestHandler::ProcessDirectQuery(contextHandle, requestJson, responseJson);
        break;

    case AIMX_PLAN_STATUS:
        hr = RequestHandler::ProcessPlanStatusQuery(contextHandle, requestJson, responseJson);
        break;

    case AIMX_EXECUTE_PLAN:
        hr = RequestHandler::ProcessExecutePlan(contextHandle, requestJson, responseJson);
        break;

    case AIMX_CANCEL_OPERATION:
        hr = RequestHandler::ProcessCancelOperation(contextHandle, requestJson, responseJson);
        break;

    default:
        TraceErr(AimxServer, "Unknown request type: %d", requestType);
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Unknown request type");
        break;
    }
    return hr;
}

HRESULT
RequestHandler::ProcessChatbotQuery(
    _In_ AIMXR_HANDLE contextHandle,
    _In_ const nlohmann::json& requestJson,
    _Out_ nlohmann::json& responseJson
    )
/*++

Routine Description:
    Processes a chatbot query request. Creates a planning operation and returns
    the operation ID for status tracking.

Arguments:
    requestJson  - The JSON request containing the chatbot query
    responseJson - The JSON response to be populated

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRequest, "Entry");
    HRESULT hr = S_OK;
    GUID operationId;
    std::shared_ptr<AIMX_OPERATION> operation;
    std::string queryStr;
    std::string guidUtf8;    

    // Validate request
    hr = ValidateCommonFields(requestJson);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "ValidateCommonFields failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Invalid request format");
        goto Exit;
    }

    // Validate chatbot-specific fields
    if (!requestJson.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_QUERY) || !requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_QUERY].is_string())
    {
        TraceErr(AimxRequest, "Missing or invalid query field");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Missing or invalid query field");
        goto Exit;
    }

    if (!requestJson.contains(AimxConstants::JsonFields::AIMX_EXECUTION_MODE) || !requestJson[AimxConstants::JsonFields::AIMX_EXECUTION_MODE].is_number_integer())
    {
        TraceErr(AimxRequest, "Missing or invalid executionMode field");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Missing or invalid executionMode field");
        goto Exit;
    }

    // Generate operation ID
    hr = CoCreateGuid(&operationId);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "CoCreateGuid failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Failed to generate operation ID");
        goto Exit;
    }

    // Create operation structure
    operation = std::make_shared<AIMX_OPERATION>();
    operation->OperationId = operationId;
    operation->RequestType = AIMX_CHATBOT_QUERY;

    int executionModeValue = requestJson[AimxConstants::JsonFields::AIMX_EXECUTION_MODE].get<int>();
    operation->ExecutionMode = static_cast<AIMX_EXECUTION_MODE>(executionModeValue);
    operation->Status = AIMX_STATUS_PLANNING;

    TraceInfo(AimxRequest, "Received execution mode: %d (AUTOMATED=%d, INTERACTIVE=%d)",
             executionModeValue, AIMX_MODE_AUTOMATED, AIMX_MODE_INTERACTIVE);

    // TEMPORARY: Force automated mode for testing
    if (operation->ExecutionMode == AIMX_MODE_INTERACTIVE)
    {
        TraceInfo(AimxRequest, "TEMPORARY: Overriding INTERACTIVE mode to AUTOMATED for testing");
        operation->ExecutionMode = AIMX_MODE_AUTOMATED;
    }

    // Convert query to wide string
    queryStr = requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_QUERY].get<std::string>();
    operation->OriginalQuery = Utf8ToWide(queryStr);

    GetSystemTimeAsFileTime(&operation->CreationTime);

    // Store operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        g_OperationMap[operationId] = operation;
    }

    // Store OperationId -> Context GUID mapping
    GUID contextGuid = ((PAIMX_HANDLE)contextHandle)->ContextId;
    {
        std::lock_guard<std::mutex> lock2(g_OperationToContextMapMutex);
        g_OperationToContextMap[operationId] = contextGuid;
    }

    {
        // Get or create conversation session
        std::shared_ptr<ConversationSession> session;
        hr = ConversationSessionManager::GetOrCreateSession(contextGuid, session);
        if (FAILED(hr))
        {
            TraceErr(AimxRequest, "Failed to get conversation session: %!HRESULT!", hr);       

            // Remove operation from map on failure
            {
                std::lock_guard<std::mutex> lock(g_OperationMapMutex);
                g_OperationMap.erase(operationId);
            }

            CreateErrorResponse(responseJson, hr, "Failed to create conversation session");
            goto Exit;
        }

        // Send user input message to conversation session
        session->SendMessage(AIMX_MSG_USER_INPUT, operation->OriginalQuery);

        // Notify session that operation started
        ConversationSessionManager::NotifyOperationStarted(contextGuid, operationId);

        // === ENHANCED REQUEST VALIDATION - THREE GATES ===
        TraceInfo(AimxRequest, "Starting enhanced request validation");

        // Prepare validation context
        AIMX_VALIDATION_CONTEXT validationContext;
        validationContext.UserQuery = operation->OriginalQuery;
        validationContext.ContextGuid = WideToUtf8(L""); // Convert GUID if needed
        validationContext.ExecutionMode = operation->ExecutionMode;
        validationContext.ConversationSession = session;

        // Run validation
        AIMX_VALIDATION_RESPONSE validationResponse;
        hr = RequestValidator::ValidateRequest(validationContext, validationResponse);
        if (FAILED(hr))
        {
            TraceErr(AimxRequest, "Request validation failed: %!HRESULT!", hr);

            // Remove operation from map on failure
            {
                std::lock_guard<std::mutex> lock(g_OperationMapMutex);
                g_OperationMap.erase(operationId);
            }

            CreateErrorResponse(responseJson, hr, "Request validation failed");
            goto Exit;
        }

        // Check if validation passed
        if (!validationResponse.ShouldContinueProcessing)
        {
            TraceInfo(AimxRequest, "Request validation blocked processing. Result: %d", validationResponse.Result);

            // Remove operation from map since we're not proceeding
            {
                std::lock_guard<std::mutex> lock(g_OperationMapMutex);
                g_OperationMap.erase(operationId);
            }

            // Create appropriate response based on validation result
            hr = RequestValidator::CreateValidationErrorResponse(
                validationResponse.Result,
                validationResponse.Message,
                validationResponse.DetailedReason,
                responseJson
            );

            if (FAILED(hr))
            {
                CreateErrorResponse(responseJson, E_FAIL, "Failed to create validation response");
            }

            goto Exit;
        }

        TraceInfo(AimxRequest, "Request validation passed - proceeding with normal processing");
    }

    TraceInfo(AimxRequest, "Kicking off planning asynchronously");

    // Start planning asynchronously
    hr = Planner::StartPlanningAsync(operationId, operation->OriginalQuery, operation->ExecutionMode);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "StartPlanningAsync failed: %!HRESULT!", hr);
        
        // Remove operation from map on failure
        {
            std::lock_guard<std::mutex> lock(g_OperationMapMutex);
            g_OperationMap.erase(operationId);
        }
        
        CreateErrorResponse(responseJson, hr, "Failed to start planning operation");
        goto Exit;
    }

    // Build success response
    responseJson[AimxConstants::JsonFields::AIMX_SUCCESS] = true;

    // Convert GUID to string for JSON
    wchar_t guidString[40];
    StringFromGUID2(operationId, guidString, ARRAYSIZE(guidString));
    guidUtf8 = WideToUtf8(guidString);

    responseJson[AimxConstants::JsonFields::AIMX_OPERATION_ID] = guidUtf8;
    responseJson[AimxConstants::JsonFields::AIMX_STATUS] = AimxConstants::StatusStrings::AIMX_STATUS_STR_PLANNING;
    responseJson[AimxConstants::JsonFields::AIMX_MESSAGE] = AimxConstants::Messages::AIMX_MSG_PLANNING_STARTED;

    TraceInfo(AimxRequest, responseJson["message"]);

Exit:
    TraceInfo(AimxRequest, "Exit, hr=0x%08X", hr);
    return hr;
}

HRESULT
RequestHandler::ProcessDirectQuery(
    _In_ AIMXR_HANDLE contextHandle,
    _In_ const nlohmann::json& requestJson,
    _Out_ nlohmann::json& responseJson
    )
/*++

Routine Description:
    Processes a direct query request. Can execute immediately in automated mode
    or create a plan for approval in interactive mode.

Arguments:
    requestJson  - The JSON request containing the direct query
    responseJson - The JSON response to be populated

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRequest, "Entry");
    HRESULT hr = S_OK;
    GUID operationId;
    std::shared_ptr<AIMX_OPERATION> operation;
    std::string commandStr;
    std::string guidUtf8;

    // Validate request
    hr = ValidateCommonFields(requestJson);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "ValidateCommonFields failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Invalid request format");
        goto Exit;
    }

    // Validate direct query specific fields
    if (!requestJson.contains(AimxConstants::JsonFields::AIMX_COMMAND) || !requestJson[AimxConstants::JsonFields::AIMX_COMMAND].is_string())
    {
        TraceErr(AimxRequest, "Missing or invalid command field");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Missing or invalid command field");
        goto Exit;
    }

    if (!requestJson.contains(AimxConstants::JsonFields::AIMX_EXECUTION_MODE) || !requestJson[AimxConstants::JsonFields::AIMX_EXECUTION_MODE].is_number_integer())
    {
        TraceErr(AimxRequest, "Missing or invalid executionMode field");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Missing or invalid executionMode field");
        goto Exit;
    }

    // Generate operation ID
    hr = CoCreateGuid(&operationId);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "CoCreateGuid failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Failed to generate operation ID");
        goto Exit;
    }

    // Create operation structure
    operation = std::make_shared<AIMX_OPERATION>();
    operation->OperationId = operationId;
    operation->RequestType = AIMX_DIRECT_QUERY;
    operation->ExecutionMode = static_cast<AIMX_EXECUTION_MODE>(requestJson[AimxConstants::JsonFields::AIMX_EXECUTION_MODE].get<int>());

    // Convert command to wide string
    commandStr = requestJson[AimxConstants::JsonFields::AIMX_COMMAND].get<std::string>();
    operation->OriginalQuery = Utf8ToWide(commandStr);

    GetSystemTimeAsFileTime(&operation->CreationTime);

    // Store operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        g_OperationMap[operationId] = operation;
    }
    // Store OperationId -> Context GUID mapping
    GUID contextGuid = ((PAIMX_HANDLE)contextHandle)->ContextId;
    {
        std::lock_guard<std::mutex> lock2(g_OperationToContextMapMutex);
        g_OperationToContextMap[operationId] = contextGuid;
    }

    {
        // Get or create conversation session
        std::shared_ptr<ConversationSession> session;
        hr = ConversationSessionManager::GetOrCreateSession(contextGuid, session);
        if (FAILED(hr))
        {
            TraceErr(AimxRequest, "Failed to get conversation session: %!HRESULT!", hr);       

            // Remove operation from map on failure
            {
                std::lock_guard<std::mutex> lock(g_OperationMapMutex);
                g_OperationMap.erase(operationId);
            }

            CreateErrorResponse(responseJson, hr, "Failed to create conversation session");
            goto Exit;
        }

        // Send user input message to conversation session
        session->SendMessage(AIMX_MSG_USER_INPUT, operation->OriginalQuery);

        // Notify session that operation started
        ConversationSessionManager::NotifyOperationStarted(contextGuid, operationId);
    }

    // Process based on execution mode
    if (operation->ExecutionMode == AIMX_MODE_AUTOMATED)
    {
        // For automated mode, create simple plan and execute
        operation->Status = AIMX_STATUS_PLANNING;
        hr = Planner::CreateSimplePlan(operationId, operation->OriginalQuery);
        if (SUCCEEDED(hr))
        {
            operation->Status = AIMX_STATUS_EXECUTING;
            hr = Orchestrator::ExecuteOperationAsync(operationId);
        }
    }
    else
    {
        // For interactive mode, create plan and wait for approval
        operation->Status = AIMX_STATUS_PLANNING;
        hr = Planner::StartPlanningAsync(operationId, operation->OriginalQuery, operation->ExecutionMode);
    }

    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "Operation processing failed: %!HRESULT!", hr);
        
        // Remove operation from map on failure
        {
            std::lock_guard<std::mutex> lock(g_OperationMapMutex);
            g_OperationMap.erase(operationId);
        }
        
        CreateErrorResponse(responseJson, hr, "Failed to process direct query");
        goto Exit;
    }

    // Build success response
    responseJson[AimxConstants::JsonFields::AIMX_SUCCESS] = true;

    // Convert GUID to string for JSON
    wchar_t guidString[40];
    StringFromGUID2(operationId, guidString, ARRAYSIZE(guidString));
    guidUtf8 = WideToUtf8(guidString);

    responseJson[AimxConstants::JsonFields::AIMX_OPERATION_ID] = guidUtf8;

    if (operation->ExecutionMode == AIMX_MODE_AUTOMATED)
    {
        responseJson[AimxConstants::JsonFields::AIMX_STATUS] = AimxConstants::StatusStrings::AIMX_STATUS_STR_EXECUTING;
        responseJson[AimxConstants::JsonFields::AIMX_MESSAGE] = AimxConstants::Messages::AIMX_MSG_EXECUTION_STARTED;
    }
    else
    {
        responseJson[AimxConstants::JsonFields::AIMX_STATUS] = AimxConstants::StatusStrings::AIMX_STATUS_STR_PLANNING;
        responseJson[AimxConstants::JsonFields::AIMX_MESSAGE] = AimxConstants::Messages::AIMX_MSG_PLANNING_STARTED;
    }

    TraceInfo(AimxRequest, responseJson["message"]);

Exit:
    TraceInfo(AimxRequest, "Exit, hr=0x%08X", hr);
    return hr;
}

HRESULT
RequestHandler::ProcessPlanStatusQuery(
    _In_ AIMXR_HANDLE contextHandle,
    _In_ const nlohmann::json& requestJson,
    _Out_ nlohmann::json& responseJson
    )
/*++

Routine Description:
    Processes a plan status query request. Returns the current status of an operation.

Arguments:
    requestJson  - The JSON request containing the operation ID
    responseJson - The JSON response to be populated

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRequest, "Entry");
    HRESULT hr = S_OK;
    GUID operationId;
    std::shared_ptr<AIMX_OPERATION> operation;
    std::string operationIdStr;
    std::wstring operationIdWStr;
    std::string statusStr;

    // Validate request
    hr = ValidateCommonFields(requestJson);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "ValidateCommonFields failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Invalid request format");
        goto Exit;
    }

    // Validate operation ID field
    if (!requestJson.contains(AimxConstants::JsonFields::AIMX_OPERATION_ID) || !requestJson[AimxConstants::JsonFields::AIMX_OPERATION_ID].is_string())
    {
        TraceErr(AimxRequest, "Missing or invalid operationId field");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Missing or invalid operationId field");
        goto Exit;
    }

    // Convert string to GUID
    operationIdStr = requestJson[AimxConstants::JsonFields::AIMX_OPERATION_ID].get<std::string>();
    operationIdWStr = Utf8ToWide(operationIdStr);

    hr = CLSIDFromString(operationIdWStr.c_str(), &operationId);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "CLSIDFromString failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Invalid operation ID format");
        goto Exit;
    }


    // Validate OperationId belongs to context
    GUID contextGuid = ((PAIMX_HANDLE)contextHandle)->ContextId;
    if (!IsOperationOwnedByContext(operationId, contextGuid)) {
        TraceErr(AimxRequest, "OperationId does not belong to context");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "OperationId does not belong to context");
        goto Exit;
    }

    // Find operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(operationId);
        if (it == g_OperationMap.end())
        {
            TraceErr(AimxRequest, "Operation not found");
            hr = E_INVALIDARG;
            CreateErrorResponse(responseJson, hr, "Operation not found");
            goto Exit;
        }
        operation = it->second;
    }

    // Build response with current status
    responseJson[AimxConstants::JsonFields::AIMX_SUCCESS] = true;
    responseJson[AimxConstants::JsonFields::AIMX_OPERATION_ID] = operationIdStr;
    
    // Convert status to string
    switch (operation->Status)
    {
        case AIMX_STATUS_PLANNING:
            statusStr = AimxConstants::StatusStrings::AIMX_STATUS_STR_PLANNING;
            break;

        case AIMX_STATUS_PLAN_READY:
            statusStr = AimxConstants::StatusStrings::AIMX_STATUS_STR_PLAN_READY;
            // Include execution plan if available
            if (!operation->ExecutionPlan.is_null())
            {
                responseJson[AimxConstants::JsonFields::AIMX_PLAN] = operation->ExecutionPlan.dump();
            }
            else
            {
                responseJson[AimxConstants::JsonFields::AIMX_PLAN] = AimxConstants::Messages::AIMX_MSG_NO_PLAN_AVAILABLE;
            }
            break;

        case AIMX_STATUS_EXECUTING:
            statusStr = AimxConstants::StatusStrings::AIMX_STATUS_STR_EXECUTING;
            break;

        case AIMX_STATUS_COMPLETED:
            statusStr = AimxConstants::StatusStrings::AIMX_STATUS_STR_COMPLETED;
            // Include result if available
            if (!operation->Result.empty())
            {
                std::string resultUtf8 = WideToUtf8(operation->Result);
                responseJson[AimxConstants::JsonFields::AIMX_RESULT] = resultUtf8;
            }
            break;

        case AIMX_STATUS_FAILED:
            statusStr = AimxConstants::StatusStrings::AIMX_STATUS_STR_FAILED;
            break;

        case AIMX_STATUS_CANCELLED:
            statusStr = AimxConstants::StatusStrings::AIMX_STATUS_STR_CANCELLED;
            break;

        default:
            statusStr = AimxConstants::StatusStrings::AIMX_STATUS_STR_UNKNOWN;
            break;
    }

    responseJson[AimxConstants::JsonFields::AIMX_STATUS] = statusStr;

Exit:
    TraceInfo(AimxRequest, "Exit, hr=0x%08X", hr);
    return hr;
}

HRESULT
RequestHandler::ProcessExecutePlan(
    _In_ AIMXR_HANDLE contextHandle,
    _In_ const nlohmann::json& requestJson,
    _Out_ nlohmann::json& responseJson
    )
/*++

Routine Description:
    Processes an execute plan request. Starts execution of a previously created plan.

Arguments:
    requestJson  - The JSON request containing the operation ID
    responseJson - The JSON response to be populated

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRequest, "Entry");
    HRESULT hr = S_OK;
    GUID operationId;
    std::shared_ptr<AIMX_OPERATION> operation;
    std::string operationIdStr;
    std::wstring operationIdWStr;

    // Validate request
    hr = ValidateCommonFields(requestJson);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "ValidateCommonFields failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Invalid request format");
        goto Exit;
    }

    // Validate operation ID field
    if (!requestJson.contains(AimxConstants::JsonFields::AIMX_OPERATION_ID) || !requestJson[AimxConstants::JsonFields::AIMX_OPERATION_ID].is_string())
    {
        TraceErr(AimxRequest, "Missing or invalid operationId field");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Missing or invalid operationId field");
        goto Exit;
    }

    // Convert string to GUID
    operationIdStr = requestJson[AimxConstants::JsonFields::AIMX_OPERATION_ID].get<std::string>();
    operationIdWStr = Utf8ToWide(operationIdStr);

    hr = CLSIDFromString(operationIdWStr.c_str(), &operationId);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "CLSIDFromString failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Invalid operation ID format");
        goto Exit;
    }


    // Validate OperationId belongs to context
    GUID contextGuid = ((PAIMX_HANDLE)contextHandle)->ContextId;
    if (!IsOperationOwnedByContext(operationId, contextGuid)) {
        TraceErr(AimxRequest, "OperationId does not belong to context");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "OperationId does not belong to context");
        goto Exit;
    }

    // Find operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(operationId);
        if (it == g_OperationMap.end())
        {
            TraceErr(AimxRequest, "Operation not found");
            hr = E_INVALIDARG;
            CreateErrorResponse(responseJson, hr, "Operation not found");
            goto Exit;
        }
        operation = it->second;
    }

    // Validate operation is ready for execution
    if (operation->Status != AIMX_STATUS_PLAN_READY)
    {
        TraceErr(AimxRequest, "Operation is not ready for execution. Current status: %d", operation->Status);
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Operation is not ready for execution");
        goto Exit;
    }

    // Start execution
    operation->Status = AIMX_STATUS_EXECUTING;
    hr = Orchestrator::ExecuteOperationAsync(operationId);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "ExecuteOperationAsync failed: %!HRESULT!", hr);
        operation->Status = AIMX_STATUS_FAILED;
        CreateErrorResponse(responseJson, hr, "Failed to start operation execution");
        goto Exit;
    }

    // Build success response
    responseJson[AimxConstants::JsonFields::AIMX_SUCCESS] = true;
    responseJson[AimxConstants::JsonFields::AIMX_OPERATION_ID] = operationIdStr;
    responseJson[AimxConstants::JsonFields::AIMX_STATUS] = AimxConstants::StatusStrings::AIMX_STATUS_STR_EXECUTING;
    responseJson[AimxConstants::JsonFields::AIMX_MESSAGE] = AimxConstants::Messages::AIMX_MSG_EXECUTION_STARTED;

    TraceInfo(AimxRequest, responseJson["message"]);

Exit:
    TraceInfo(AimxRequest, "Exit, hr=0x%08X", hr);
    return hr;
}

HRESULT
RequestHandler::ProcessCancelOperation(
    _In_ AIMXR_HANDLE contextHandle,
    _In_ const nlohmann::json& requestJson,
    _Out_ nlohmann::json& responseJson
    )
/*++

Routine Description:
    Processes a cancel operation request. Cancels a running or planned operation.

Arguments:
    requestJson  - The JSON request containing the operation ID
    responseJson - The JSON response to be populated

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRequest, "Entry");
    HRESULT hr = S_OK;
    GUID operationId;
    std::shared_ptr<AIMX_OPERATION> operation;
    std::string operationIdStr;
    std::wstring operationIdWStr;

    // Validate request
    hr = ValidateCommonFields(requestJson);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "ValidateCommonFields failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Invalid request format");
        goto Exit;
    }

    // Validate operation ID field
    if (!requestJson.contains(AimxConstants::JsonFields::AIMX_OPERATION_ID) || !requestJson[AimxConstants::JsonFields::AIMX_OPERATION_ID].is_string())
    {
        TraceErr(AimxRequest, "Missing or invalid operationId field");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Missing or invalid operationId field");
        goto Exit;
    }

    // Convert string to GUID
    operationIdStr = requestJson[AimxConstants::JsonFields::AIMX_OPERATION_ID].get<std::string>();
    operationIdWStr = Utf8ToWide(operationIdStr);

    hr = CLSIDFromString(operationIdWStr.c_str(), &operationId);
    if (FAILED(hr))
    {
        TraceErr(AimxRequest, "CLSIDFromString failed: %!HRESULT!", hr);
        CreateErrorResponse(responseJson, hr, "Invalid operation ID format");
        goto Exit;
    }


    // Validate OperationId belongs to context
    GUID contextGuid = ((PAIMX_HANDLE)contextHandle)->ContextId;
    if (!IsOperationOwnedByContext(operationId, contextGuid)) {
        TraceErr(AimxRequest, "OperationId does not belong to context");
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "OperationId does not belong to context");
        goto Exit;
    }

    // Find operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(operationId);
        if (it == g_OperationMap.end())
        {
            TraceErr(AimxRequest, "Operation not found");
            hr = E_INVALIDARG;
            CreateErrorResponse(responseJson, hr, "Operation not found");
            goto Exit;
        }
        operation = it->second;
    }

    // Check if operation can be cancelled
    if (operation->Status == AIMX_STATUS_COMPLETED || operation->Status == AIMX_STATUS_CANCELLED)
    {
        TraceErr(AimxRequest, "Operation cannot be cancelled. Current status: %d", operation->Status);
        hr = E_INVALIDARG;
        CreateErrorResponse(responseJson, hr, "Operation cannot be cancelled");
        goto Exit;
    }

    // Cancel the operation
    if (operation->Status == AIMX_STATUS_EXECUTING)
    {
        hr = Orchestrator::CancelOperation(operationId);
        if (FAILED(hr))
        {
        TraceErr(AimxRequest, "Orchestrator::CancelOperation failed: %!HRESULT!", hr);
            CreateErrorResponse(responseJson, hr, "Failed to cancel executing operation");
            goto Exit;
        }
    }
    else if (operation->Status == AIMX_STATUS_PLANNING)
    {
        hr = Planner::CancelPlanning(operationId);
        if (FAILED(hr))
        {
        TraceErr(AimxRequest, "Planner::CancelPlanning failed: %!HRESULT!", hr);
            CreateErrorResponse(responseJson, hr, "Failed to cancel planning operation");
            goto Exit;
        }
    }

    // Update operation status
    operation->Status = AIMX_STATUS_CANCELLED;
    GetSystemTimeAsFileTime(&operation->CompletionTime);

    // Build success response
    responseJson[AimxConstants::JsonFields::AIMX_SUCCESS] = true;
    responseJson[AimxConstants::JsonFields::AIMX_OPERATION_ID] = operationIdStr;
    responseJson[AimxConstants::JsonFields::AIMX_STATUS] = AimxConstants::StatusStrings::AIMX_STATUS_STR_CANCELLED;
    responseJson[AimxConstants::JsonFields::AIMX_MESSAGE] = AimxConstants::Messages::AIMX_MSG_CANCELLED;

    TraceInfo(AimxRequest, responseJson["message"]);

Exit:
    TraceInfo(AimxRequest, "Exit, hr=0x%08X", hr);
    return hr;
}

HRESULT
RequestHandler::ValidateCommonFields(
    _In_ const nlohmann::json& requestJson
    )
/*++

Routine Description:
    Validates common fields that should be present in all requests.

Arguments:
    requestJson - The JSON request to validate

Return Value:
    S_OK if validation passes, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;

    // Check if requestJson is an object
    if (!requestJson.is_object())
    {
        TraceErr(AimxRequest, "Request is not a JSON object");
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Validate requestType field
    if (!requestJson.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_REQUEST_TYPE) || !requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_REQUEST_TYPE].is_number_integer())
    {
        TraceErr(AimxRequest, "Missing or invalid requestType field");
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Validate requestType value
    int requestType = requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_REQUEST_TYPE].get<int>();
    if (requestType < 1 || requestType > 5)
    {
        TraceErr(AimxRequest, "Invalid requestType value: %d", requestType);
        hr = E_INVALIDARG;
        goto Exit;
    }

Exit:
    return hr;
}

void
RequestHandler::CreateErrorResponse(
    _Out_ nlohmann::json& responseJson,
    _In_ HRESULT errorCode,
    _In_ const std::string& errorMessage
    )
/*++

Routine Description:
    Creates a standardized error response JSON structure using shared library utilities.

Arguments:
    responseJson - The JSON response to populate
    errorCode    - The HRESULT error code
    errorMessage - The error message string

Return Value:
    None.

--*/
{
    TraceInfo(AimxRequest, "Entry");

    // Use shared library for consistent error response format
    int mcpErrorCode = McpProtocol::Utils::HResultToMcpErrorCode(errorCode);
    nlohmann::json errorData = {
        {"hresult", static_cast<int>(errorCode)}
    };

    responseJson = McpProtocol::JsonRpc::CreateErrorResponse(
        nlohmann::json(),
        mcpErrorCode,
        errorMessage,
        errorData
    );

    // Add AIMX-specific fields for backward compatibility
    responseJson[AimxConstants::JsonFields::AIMX_SUCCESS] = false;
    responseJson[AimxConstants::JsonFields::AIMX_JSON_KEY_ERROR_CODE] = static_cast<int>(errorCode);
    responseJson[AimxConstants::JsonFields::AIMX_JSON_KEY_ERROR_MESSAGE] = errorMessage;

    TraceInfo(AimxRequest, "Exit");
}

